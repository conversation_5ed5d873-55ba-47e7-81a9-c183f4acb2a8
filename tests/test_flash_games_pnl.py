import pytest
from unittest.mock import MagicMock, patch
from pyspark.sql import SparkSession
from pyspark.sql import Row
import pandas as pd

import sys
import os
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '../src/jobs/trading_competition')))
from flash_games_pnl import FlashGamesPNL

@pytest.fixture(scope="session")
def spark():
    return SparkSession.builder.master("local[1]").appName("unit-tests").getOrCreate()

@pytest.fixture
def mock_io_utils():
    mock = MagicMock()
    mock.read_parquet_data.return_value = None  # Will be set in test
    mock.write_parquet_file.return_value = None
    mock.write_csv_file.return_value = None
    mock.write_dataset_to_mongo.return_value = None
    return mock

@pytest.fixture
def mock_logger():
    return MagicMock()

@pytest.fixture
def flash_games_pnl(spark, mock_io_utils, mock_logger):
    # Patch dependencies as needed
    with patch('src.jobs.trading_competition.flash_games_pnl.IOUtils', new=mock_io_utils):
        obj = FlashGamesPNL()
        obj.io_utils = mock_io_utils
        obj.logger = mock_logger
        obj.spark = spark
        obj.utc_cutoff_ts = **********
        obj.flash_game_start_ts = **********
        obj.flash_games_batch_path = '/tmp/flash_games_batch_path'
        obj.t_1 = '2025-07-09'
        obj.h_1 = '10'
        obj.transactions_overwrite_path = '/tmp/transactions.parquet'
        return obj

def make_transactions_df(spark):
    data = [
        Row(account_id=1, user_id=101, asset_id='BTC', leverage=2, fees=0.1, asset_type='crypto', transaction_id='txn1',
            created=**********, updated=**********, updated_executed_quantity=1.0, updated_executed_unit_price=50000.0,
            transaction_type='buy', currency_to_idr=14000, transaction_time=**********, asset_sub_type='spot',
            current_unit_price=51000.0, current_currency_to_idr=14000),
        Row(account_id=2, user_id=102, asset_id='ETH', leverage=1, fees=0.05, asset_type='crypto', transaction_id='txn2',
            created=**********, updated=**********, updated_executed_quantity=2.0, updated_executed_unit_price=3000.0,
            transaction_type='sell', currency_to_idr=14000, transaction_time=**********, asset_sub_type='spot',
            current_unit_price=3200.0, current_currency_to_idr=14000)
    ]
    return spark.createDataFrame(data)

def make_assets_df(spark):
    data = [Row(asset_id='BTC'), Row(asset_id='ETH')]
    return spark.createDataFrame(data)

def test_get_all_eligible_transactions(flash_games_pnl, spark):
    assets_df = make_assets_df(spark)
    txns_df = make_transactions_df(spark)
    # Patch col to just return the column name for filter
    with patch('src.jobs.trading_competition.flash_games_pnl.col', side_effect=lambda x: x):
        eligible = flash_games_pnl.get_all_eligible_transactions(assets_df, txns_df)
        assert eligible is not None
        # Add more assertions as per your logic

def test_create_initial_position(flash_games_pnl, spark):
    txns_df = make_transactions_df(spark)
    result = flash_games_pnl.create_initial_position(txns_df)
    assert result is not None
    # Add more assertions as per your logic

def test_execute_with_no_active_flash_game(flash_games_pnl):
    flash_games_pnl.get_current_flash_game = MagicMock(return_value=None)
    flash_games_pnl.logger = MagicMock()
    flash_games_pnl.execute()
    flash_games_pnl.logger.info.assert_called()

def test_execute_with_active_flash_game(flash_games_pnl, spark):
    flash_games_pnl.get_current_flash_game = MagicMock(return_value={'flash_game_name': 'FG1'})
    flash_games_pnl.get_current_flash_game_asset_id = MagicMock(return_value=make_assets_df(spark))
    flash_games_pnl.io_utils.read_parquet_data.return_value = make_transactions_df(spark)
    flash_games_pnl.get_all_eligible_transactions = MagicMock(return_value=make_transactions_df(spark))
    flash_games_pnl.create_initial_position = MagicMock(return_value=make_transactions_df(spark))
    flash_games_pnl.get_batches = MagicMock(return_value=make_transactions_df(spark))
    flash_games_pnl.cast_fields = MagicMock(return_value=make_transactions_df(spark))
    flash_games_pnl.io_utils.write_parquet_file = MagicMock()
    flash_games_pnl.io_utils.write_csv_file = MagicMock()
    flash_games_pnl.execute()
    flash_games_pnl.io_utils.write_parquet_file.assert_called()
    flash_games_pnl.io_utils.write_csv_file.assert_called()

