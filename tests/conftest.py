import pytest
import os
import sys
from datetime import datetime, timedelta
from unittest.mock import Mock, MagicMock, patch
from pyspark.sql import SparkSession
from pyspark.sql.types import *
from pyspark.sql import functions as F

# Add src to path for imports
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'src'))

@pytest.fixture(scope="session")
def spark_session():
    """Create a Spark session for testing."""
    spark = SparkSession.builder \
        .appName("FlashGamesPnL_Test") \
        .master("local[2]") \
        .config("spark.sql.warehouse.dir", "/tmp/spark-warehouse") \
        .config("spark.sql.adaptive.enabled", "false") \
        .config("spark.sql.adaptive.coalescePartitions.enabled", "false") \
        .config("spark.driver.bindAddress", "127.0.0.1") \
        .config("spark.driver.host", "127.0.0.1") \
        .getOrCreate()

    # Set log level to reduce noise
    spark.sparkContext.setLogLevel("WARN")

    yield spark
    spark.stop()

@pytest.fixture
def mock_config():
    """Mock configuration for FlashGamesPnL."""
    return {
        "bucket_path": "s3a://test-bucket",
        "snapshot_path": "snapshots",
        "batches": {
            "batch_file_path": "batches",
            "flash_games_batch_file_path": "flash_games_batches",
            "all_transaction_file_path": "all_transactions"
        },
        "trading_competition": {
            "id": "TC_2025_Q1",
            "start_time": "2025-01-01 00:00:00.000",
            "frequency": 24
        },
        "aum_tier_upgrade": {
            "tier_snapshot_path": "tier_snapshots"
        },
        "flash_games": {
            "game_1": {
                "start_ts": "2025-01-15 10:00:00.000",
                "end_ts": "2025-01-15 18:00:00.000",
                "assets": {
                    "global_stocks": [1, 2, 3],
                    "crypto_currency": [101, 102],
                    "global_stock_options": [],
                    "global_stock_with_leverage": []
                }
            }
        },
        "buy_types": ["BUY", "AIRDROP_BUY"],
        "sell_types": ["SELL", "AIRDROP_SELL"],
        "bootstrap_servers": "localhost:9092",
        "kafka_topics": {
            "global_stock_topic": "global_stocks"
        },
        "niv_path": "niv",
        "gtv_path": "gtv",
        "flash_games_assets_path": "flash_games_assets",
        "flash_games_pnl_path": "flash_games_pnl",
        "data_store": {
            "reporting_mongo": {
                "host": "localhost",
                "port": 27017,
                "database": "test_db",
                "username": "test_user",
                "password": "test_pass"
            },
            "flash_game": {
                "collection": "flash_game_leaderboard"
            }
        },
        "utc_cutoff_ts": datetime(2024, 1, 15, 14, 0, 0),  # During flash game
        "offset": 0,
        "execution_time": "jkt_day_end"
    }

@pytest.fixture
def mock_logger():
    """Mock logger."""
    logger = Mock()
    logger.info = Mock()
    logger.warning = Mock()
    logger.error = Mock()
    logger.debug = Mock()
    return logger

@pytest.fixture
def mock_spark_utils():
    """Mock SparkUtils."""
    spark_utils = Mock()
    spark_utils.create_spark_session = Mock()
    spark_utils.stop_spark = Mock()
    return spark_utils

@pytest.fixture
def mock_io_utils():
    """Mock IOUtils."""
    io_utils = Mock()
    io_utils.read_parquet_data = Mock()
    io_utils.read_csv_file = Mock()
    io_utils.read_from_kafka_in_memory = Mock()
    io_utils.write_parquet_file = Mock()
    io_utils.write_csv_file = Mock()
    io_utils.write_dataset_to_mongo = Mock()
    io_utils.get_mongo_connection_string = Mock(return_value="*******************************************")
    return io_utils

@pytest.fixture
def mock_operations():
    """Mock Operations."""
    ops = Mock()
    return ops

@pytest.fixture
def mock_date_utils():
    """Mock DateUtils."""
    with patch('src.jobs.trading_competition.flash_games_pnl.DateUtils') as mock_date_utils:
        mock_date_utils.get_utc_timestamp_from_string.return_value = datetime(2024, 1, 15, 10, 0, 0)
        mock_date_utils.get_utc_timestamp.return_value = datetime(2024, 1, 15, 14, 0, 0)
        mock_date_utils.get_tc_dates_and_timestamp.return_value = (
            datetime(2024, 1, 15).date(),  # t_1
            14,  # h_1
            datetime(2024, 1, 14).date(),  # t_2
            14,  # h_2
            "2024-01-15T14:00:00.000Z",  # dt_1
            "2024-01-14T17:00:00.000Z"   # dt_2
        )
        yield mock_date_utils

@pytest.fixture
def sample_transactions_data(spark_session):
    """Sample transaction data for testing."""
    data = [
        (1001, 101, 1, 1, 0.0, "global_stocks", 1001,
         datetime(2024, 1, 15, 11, 0, 0), datetime(2024, 1, 15, 11, 0, 0),
         100.0, 50.0, "BUY", 15000.0, datetime(2024, 1, 15, 11, 0, 0),
         "global_stock_transactions", 52.0, 15100),
        (1001, 101, 1, 1, 5.0, "global_stocks", 1002,
         datetime(2024, 1, 15, 12, 0, 0), datetime(2024, 1, 15, 12, 0, 0),
         50.0, 55.0, "SELL", 15200.0, datetime(2024, 1, 15, 12, 0, 0),
         "global_stock_transactions", 55.0, 15200),
        (1002, 102, 2, 1, 0.0, "crypto_currency", 1003,
         datetime(2024, 1, 15, 13, 0, 0), datetime(2024, 1, 15, 13, 0, 0),
         1000.0, 0.001, "BUY", 1.0, datetime(2024, 1, 15, 13, 0, 0),
         "crypto_currency_transactions", 0.0011, 1)
    ]

    schema = StructType([
        StructField("account_id", LongType(), True),
        StructField("user_id", LongType(), True),
        StructField("asset_id", LongType(), True),
        StructField("leverage", LongType(), True),
        StructField("fees", DoubleType(), True),
        StructField("asset_type", StringType(), True),
        StructField("transaction_id", LongType(), True),
        StructField("created", TimestampType(), True),
        StructField("updated", TimestampType(), True),
        StructField("updated_executed_quantity", DoubleType(), True),
        StructField("updated_executed_unit_price", DoubleType(), True),
        StructField("transaction_type", StringType(), True),
        StructField("currency_to_idr", DoubleType(), True),
        StructField("transaction_time", TimestampType(), True),
        StructField("asset_sub_type", StringType(), True),
        StructField("current_unit_price", DoubleType(), True),
        StructField("current_currency_to_idr", LongType(), True)
    ])

    return spark_session.createDataFrame(data, schema)

@pytest.fixture
def sample_asset_data(spark_session):
    """Sample asset data for testing."""
    data = [
        ("global_stocks", 1),
        ("global_stocks", 2),
        ("crypto_currency", 101),
        ("crypto_currency", 102)
    ]
    
    schema = StructType([
        StructField("asset_type", StringType(), True),
        StructField("asset_id", LongType(), True)
    ])
    
    return spark_session.createDataFrame(data, schema)

@pytest.fixture
def sample_options_data(spark_session):
    """Sample options contracts data."""
    data = [
        (1001, 1),
        (1002, 2)
    ]
    
    schema = StructType([
        StructField("id", LongType(), True),
        StructField("global_stock_id", LongType(), True)
    ])
    
    return spark_session.createDataFrame(data, schema)

@pytest.fixture
def sample_leverage_stocks_data(spark_session):
    """Sample leverage stocks data."""
    data = [
        ({"id": 1, "status": "ACTIVE", "stock_type": "CFD_LEVERAGE"},),
        ({"id": 2, "status": "ACTIVE", "stock_type": "CFD_LEVERAGE"},),
        ({"id": 3, "status": "INACTIVE", "stock_type": "CFD_LEVERAGE"},)
    ]
    
    schema = StructType([
        StructField("value", MapType(StringType(), StringType()), True)
    ])
    
    return spark_session.createDataFrame(data, schema)

@pytest.fixture
def sample_niv_data(spark_session):
    """Sample NIV data."""
    data = [
        (1001, 1000000.0),
        (1002, 500000.0)
    ]
    
    schema = StructType([
        StructField("account_id", LongType(), True),
        StructField("invested_value", DoubleType(), True)
    ])
    
    return spark_session.createDataFrame(data, schema)

@pytest.fixture
def sample_gtv_data(spark_session):
    """Sample GTV data."""
    data = [
        (1001, 1200000.0),
        (1002, 600000.0)
    ]
    
    schema = StructType([
        StructField("account_id", LongType(), True),
        StructField("total_gtv", DoubleType(), True)
    ])
    
    return spark_session.createDataFrame(data, schema)

@pytest.fixture
def sample_user_details_data(spark_session):
    """Sample user details data."""
    data = [
        (1001, 101, "John Doe", "<EMAIL>", "TC_2024_Q1"),
        (1002, 102, "Jane Smith", "<EMAIL>", "TC_2024_Q1")
    ]
    
    schema = StructType([
        StructField("account_id", LongType(), True),
        StructField("user_id", LongType(), True),
        StructField("name", StringType(), True),
        StructField("email", StringType(), True),
        StructField("trading_competition_id", StringType(), True)
    ])
    
    return spark_session.createDataFrame(data, schema)
