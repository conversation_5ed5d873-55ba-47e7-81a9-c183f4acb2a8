#!/usr/bin/env python3
"""
Test runner script for FlashGamesPnL unit tests.

This script provides various options for running the test suite:
- Run all tests
- Run specific test categories (unit, integration)
- Run with coverage reporting
- Run in parallel
"""

import sys
import os
import subprocess
import argparse
from pathlib import Path

def setup_environment():
    """Setup the test environment."""
    # Add src to Python path
    src_path = Path(__file__).parent / "src"
    if str(src_path) not in sys.path:
        sys.path.insert(0, str(src_path))
    
    # Set environment variables for testing
    os.environ["PYTHONPATH"] = str(src_path)
    os.environ["SPARK_LOCAL_IP"] = "127.0.0.1"
    
    # Initialize logger for tests
    try:
        from src.utils.custom_logger import init_logger
        init_logger("test")
    except ImportError:
        print("Warning: Could not initialize logger")

def run_command(cmd, description):
    """Run a command and handle errors."""
    print(f"\n{'='*60}")
    print(f"Running: {description}")
    print(f"Command: {' '.join(cmd)}")
    print(f"{'='*60}")
    
    try:
        result = subprocess.run(cmd, check=True, capture_output=True, text=True)
        print(result.stdout)
        if result.stderr:
            print("STDERR:", result.stderr)
        return True
    except subprocess.CalledProcessError as e:
        print(f"Error running {description}:")
        print(f"Return code: {e.returncode}")
        print(f"STDOUT: {e.stdout}")
        print(f"STDERR: {e.stderr}")
        return False

def install_dependencies():
    """Install test dependencies."""
    print("Installing test dependencies...")
    cmd = [sys.executable, "-m", "pip", "install", "-r", "requirements-test.txt"]
    return run_command(cmd, "Installing test dependencies")

def run_unit_tests():
    """Run unit tests only."""
    cmd = [
        sys.executable, "-m", "pytest", 
        "tests/test_flash_games_pnl.py",
        "-v", "--tb=short", "-m", "not integration"
    ]
    return run_command(cmd, "Unit tests")

def run_integration_tests():
    """Run integration tests only."""
    cmd = [
        sys.executable, "-m", "pytest", 
        "tests/test_flash_games_pnl_integration.py",
        "-v", "--tb=short", "-m", "integration"
    ]
    return run_command(cmd, "Integration tests")

def run_transaction_transformer_tests():
    """Run TransactionTransformer tests."""
    print("Running TransactionTransformer tests...")

    # Run no-spark tests (fast)
    cmd1 = [
        sys.executable, "-m", "pytest",
        "tests/test_transaction_transformer_no_spark.py",
        "-v", "--tb=short"
    ]
    result1 = run_command(cmd1, "TransactionTransformer No-Spark tests")

    # Run Spark tests (slower) - only the ones that work with current Python/Spark version
    cmd2 = [
        sys.executable, "-m", "pytest",
        "tests/test_transaction_transformer.py::TestTransactionTransformer::test_init",
        "tests/test_transaction_transformer.py::TestTransactionTransformer::test_run",
        "-v", "--tb=short"
    ]
    result2 = run_command(cmd2, "TransactionTransformer Spark tests")

    return result1 and result2

def run_all_tests():
    """Run all tests."""
    cmd = [
        sys.executable, "-m", "pytest",
        "tests/",
        "-v", "--tb=short"
    ]
    return run_command(cmd, "All tests")

def run_tests_with_coverage():
    """Run tests with coverage reporting."""
    cmd = [
        sys.executable, "-m", "pytest", 
        "tests/",
        "-v", "--tb=short",
        "--cov=src",
        "--cov-report=html:htmlcov",
        "--cov-report=term-missing",
        "--cov-fail-under=70"
    ]
    return run_command(cmd, "Tests with coverage")

def run_tests_parallel():
    """Run tests in parallel."""
    cmd = [
        sys.executable, "-m", "pytest", 
        "tests/",
        "-v", "--tb=short",
        "-n", "auto"  # Use all available CPUs
    ]
    return run_command(cmd, "Tests in parallel")

def run_specific_test(test_name):
    """Run a specific test."""
    cmd = [
        sys.executable, "-m", "pytest", 
        f"tests/{test_name}",
        "-v", "--tb=short"
    ]
    return run_command(cmd, f"Specific test: {test_name}")

def main():
    """Main test runner function."""
    parser = argparse.ArgumentParser(description="Run FlashGamesPnL tests")
    parser.add_argument("--install-deps", action="store_true", 
                       help="Install test dependencies")
    parser.add_argument("--unit", action="store_true", 
                       help="Run unit tests only")
    parser.add_argument("--integration", action="store_true", 
                       help="Run integration tests only")
    parser.add_argument("--coverage", action="store_true", 
                       help="Run tests with coverage reporting")
    parser.add_argument("--parallel", action="store_true", 
                       help="Run tests in parallel")
    parser.add_argument("--test", type=str, 
                       help="Run specific test file")
    parser.add_argument("--all", action="store_true", 
                       help="Run all tests (default)")
    
    args = parser.parse_args()
    
    # Setup environment
    setup_environment()
    
    success = True
    
    # Install dependencies if requested
    if args.install_deps:
        success = install_dependencies() and success
    
    # Run tests based on arguments
    if args.unit:
        success = run_unit_tests() and success
    elif args.integration:
        success = run_integration_tests() and success
    elif args.coverage:
        success = run_tests_with_coverage() and success
    elif args.parallel:
        success = run_tests_parallel() and success
    elif args.test:
        success = run_specific_test(args.test) and success
    else:
        # Default: run all tests
        success = run_all_tests() and success
    
    if success:
        print("\n✅ All tests completed successfully!")
        return 0
    else:
        print("\n❌ Some tests failed!")
        return 1

if __name__ == "__main__":
    sys.exit(main())
